// 結構化資料載入器
(function() {
  const schemaData = {
    "@context": "https://schema.org",
    "@type": "ProfessionalService",
    "name": "合羽影像製作",
    "description": "專業攝影服務，讓影像成為行動，用故事照亮日常。提供 NGO、活動紀錄、新聞紀實、刊物攝影、動態影像、商業攝影等服務。",
    "url": "https://gather-feather.com",
    "image": "https://gather-feather.com/img/logo-dark.svg",
    "areaServed": {
      "@type": "Country",
      "name": "台灣"
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "攝影服務",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "NGO攝影",
            "description": "為非營利組織提供專業攝影服務"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "活動紀錄",
            "description": "專業活動攝影與紀錄服務"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "新聞紀實",
            "description": "新聞事件與紀實攝影服務"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "刊物攝影",
            "description": "雜誌、書籍等刊物攝影服務"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "動態影像",
            "description": "影片拍攝與動態影像製作"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "商業攝影",
            "description": "企業與商業用途攝影服務"
          }
        }
      ]
    },
    "sameAs": [
      "https://www.facebook.com/您的FB頁面",
      "https://www.instagram.com/您的IG帳號"
    ]
  };

  // 動態插入結構化資料到 head
  function insertSchema() {
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(schemaData, null, 2);
    document.head.appendChild(script);
  }

  // DOM 載入完成後執行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', insertSchema);
  } else {
    insertSchema();
  }
})();
